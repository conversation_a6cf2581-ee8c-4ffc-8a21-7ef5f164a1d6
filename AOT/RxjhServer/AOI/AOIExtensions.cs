using System;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Helpers;


namespace RxjhServer.AOI
{
    /// <summary>
    /// Extension methods to integrate AOI system with existing game entities
    /// Provides seamless integration without breaking existing functionality
    /// </summary>
    public static class AOIExtensions
    {
        #region Player Extensions
        
        /// <summary>
        /// Update player's AOI using the new AOI system
        /// This replaces the old GetTheReviewRangePlayers method
        /// </summary>
        /// <param name="player">Player to update AOI for</param>
        /// <param name="useAOISystem">Whether to use the new AOI system (default: true)</param>
        public static void UpdateAOI(this Players player, bool useAOISystem = true)
        {
            try
            {
                if (player == null)
                {
                    return;
                }
                
                if (useAOISystem && AOIConfiguration.Instance.ShouldUseAOI(player.MapID))
                {
                    // Use optimized AOI system with adaptive updates
                    AOIUpdateService.Instance.AdaptiveUpdatePlayer(player);
                }
                else
                {
                    // Fallback to old system
                    player.GetTheReviewRangePlayers();
                    player.GetReviewScopeNpc();
                    player.ScanGroundItems();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating AOI for player {player.CharacterName}: {ex.Message}");
                
                // Fallback to old system on error
                try
                {
                    player.GetTheReviewRangePlayers();
                    player.GetReviewScopeNpc();
                    player.ScanGroundItems();
                }
                catch (Exception fallbackEx)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Fallback AOI update also failed: {fallbackEx.Message}");
                }
            }
        }

        /// <summary>
        /// Update player's AOI immediately without adaptive delays
        /// Use this for critical updates that need immediate processing
        /// </summary>
        /// <param name="player">Player to update AOI for</param>
        public static void UpdateAOIImmediate(this Players player)
        {
            try
            {
                if (player == null)
                {
                    return;
                }

                if (AOIConfiguration.Instance.ShouldUseAOI(player.MapID))
                {
                    // Use direct AOI update bypassing adaptive delays
                    AOIUpdateService.Instance.UpdatePlayerAOI(player);
                }
                else
                {
                    // Fallback to old system
                    player.GetTheReviewRangePlayers();
                    player.GetReviewScopeNpc();
                    player.ScanGroundItems();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in immediate AOI update for player {player.CharacterName}: {ex.Message}");

                // Fallback to old system on error
                try
                {
                    player.GetTheReviewRangePlayers();
                    player.GetReviewScopeNpc();
                    player.ScanGroundItems();
                }
                catch (Exception fallbackEx)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Fallback AOI update also failed: {fallbackEx.Message}");
                }
            }
        }

        /// <summary>
        /// Force update player's AOI regardless of throttling or optimization
        /// Use this when player changes map or other critical events
        /// </summary>
        /// <param name="player">Player to force update AOI for</param>
        public static void ForceUpdateAOI(this Players player)
        {
            try
            {
                if (player == null)
                {
                    return;
                }

                LogHelper.WriteLine(LogLevel.Debug, $"Force updating AOI for player {player.CharacterName}");

                if (AOIConfiguration.Instance.ShouldUseAOI(player.MapID))
                {
                    // Force immediate AOI update
                    AOIUpdateService.Instance.UpdatePlayerAOI(player);

                    // Also ensure player is properly added to AOI system
                    AOIManager.Instance.AddPlayer(player);
                }
                else
                {
                    // Fallback to old system
                    player.GetTheReviewRangePlayers();
                    player.GetReviewScopeNpc();
                    player.ScanGroundItems();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in force AOI update for player {player.CharacterName}: {ex.Message}");

                // Fallback to old system on error
                try
                {
                    player.GetTheReviewRangePlayers();
                    player.GetReviewScopeNpc();
                    player.ScanGroundItems();
                }
                catch (Exception fallbackEx)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Fallback force AOI update also failed: {fallbackEx.Message}");
                }
            }
        }

        /// <summary>
        /// Add player to AOI system when they enter the game
        /// </summary>
        /// <param name="player">Player to add</param>
        public static void AddToAOI(this Players player)
        {
            try
            {
                if (player != null)
                {
                    AOIManager.Instance.AddPlayer(player);
                    //LogHelper.WriteLine(LogLevel.Info, $"Player {player.CharacterName} added to AOI system");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error adding player to AOI: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Remove player from AOI system when they leave the game
        /// </summary>
        /// <param name="player">Player to remove</param>
        public static void RemoveFromAOI(this Players player)
        {
            try
            {
                if (player != null)
                {
                    AOIManager.Instance.RemovePlayer(player.SessionID);
                    LogHelper.WriteLine(LogLevel.Info, $"Player {player.CharacterName} removed from AOI system");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error removing player from AOI: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Update player position in AOI system when they move
        /// </summary>
        /// <param name="player">Player that moved</param>
        /// <param name="newX">New X coordinate</param>
        /// <param name="newY">New Y coordinate</param>
        public static void UpdateAOIPosition(this Players player, float newX, float newY)
        {
            try
            {
                if (player != null)
                {
                    AOIManager.Instance.UpdatePlayerPosition(player, newX, newY);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating player position in AOI: {ex.Message}");
            }
        }

        /// <summary>
        /// Update player's AOI specifically for movement events (optimized)
        /// </summary>
        /// <param name="player">Player that moved</param>
        public static void UpdateMovementAOI(this Players player)
        {
            try
            {
                if (player == null)
                {
                    return;
                }

                if (AOIConfiguration.Instance.ShouldUseAOI(player.MapID))
                {
                    // Use optimized movement AOI update
                    AOIUpdateService.Instance.UpdatePlayerMovementAOI(player);
                }
                else
                {
                    // Fallback to old system
                    player.GetTheReviewRangePlayers();
                    player.GetReviewScopeNpc();
                    player.ScanGroundItems();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in movement AOI update for player {player.CharacterName}: {ex.Message}");

                // Fallback to old system on error
                try
                {
                    player.GetTheReviewRangePlayers();
                    player.GetReviewScopeNpc();
                    player.ScanGroundItems();
                }
                catch (Exception fallbackEx)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Fallback movement AOI update also failed: {fallbackEx.Message}");
                }
            }
        }
        
        #endregion
        
        #region NPC Extensions
        
        /// <summary>
        /// Add NPC to AOI system when it spawns
        /// </summary>
        /// <param name="npc">NPC to add</param>
        public static void AddToAOI(this NpcClass npc)
        {
            try
            {
                if (npc != null)
                {
                    AOIManager.Instance.AddNPC(npc);
                   // LogHelper.WriteLine(LogLevel.Info, $"NPC {npc.Name} added to AOI system");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error adding NPC to AOI: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Remove NPC from AOI system when it despawns
        /// </summary>
        /// <param name="npc">NPC to remove</param>
        public static void RemoveFromAOI(this NpcClass npc)
        {
            try
            {
                if (npc != null)
                {
                    AOIManager.Instance.RemoveNPC(npc.NPC_SessionID);
                    LogHelper.WriteLine(LogLevel.Info, $"NPC {npc.Name} removed from AOI system");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error removing NPC from AOI: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Update NPC position in AOI system when it moves
        /// </summary>
        /// <param name="npc">NPC that moved</param>
        /// <param name="newX">New X coordinate</param>
        /// <param name="newY">New Y coordinate</param>
        public static void UpdateAOIPosition(this NpcClass npc, float newX, float newY)
        {
            try
            {
                if (npc != null)
                {
                    AOIManager.Instance.UpdateNPCPosition(npc, newX, newY);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating NPC position in AOI: {ex.Message}");
            }
        }
        
        #endregion
        
        #region Ground Item Extensions
        
        /// <summary>
        /// Add ground item to AOI system when it spawns
        /// </summary>
        /// <param name="item">Ground item to add</param>
        public static void AddToAOI(this GroundItem item)
        {
            try
            {
                if (item != null)
                {
                    AOIManager.Instance.AddGroundItem(item);
                   // LogHelper.WriteLine(LogLevel.Info, $"Ground item {item.id} added to AOI system");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error adding ground item to AOI: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Remove ground item from AOI system when it despawns
        /// </summary>
        /// <param name="item">Ground item to remove</param>
        public static void RemoveFromAOI(this GroundItem item)
        {
            try
            {
                if (item != null)
                {
                    AOIManager.Instance.RemoveGroundItem(item.id);
                    LogHelper.WriteLine(LogLevel.Info, $"Ground item {item.id} removed from AOI system");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error removing ground item from AOI: {ex.Message}");
            }
        }
        
        #endregion
        
        #region Batch Operations
        
        /// <summary>
        /// Update AOI for multiple players efficiently
        /// </summary>
        /// <param name="players">Players to update AOI for</param>
        public static void BatchUpdateAOI(this IEnumerable<Players> players)
        {
            try
            {
                var playerList = players?.Where(p => p != null).ToList();
                if (playerList != null && playerList.Count > 0)
                {
                    // Use optimized batch update with thread pool
                    AOIUpdateService.Instance.BatchUpdatePlayersOptimized(playerList);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in optimized batch AOI update: {ex.Message}");

                // Fallback to individual updates
                try
                {
                    var playerList = players?.Where(p => p != null).ToList();
                    if (playerList != null)
                    {
                        foreach (var player in playerList)
                        {
                            player.UpdateAOI();
                        }
                    }
                }
                catch (Exception fallbackEx)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Error in batch AOI fallback: {fallbackEx.Message}");
                }
            }
        }
        
        #endregion
        
        #region Map Integration
        
        /// <summary>
        /// Initialize AOI system for a map
        /// </summary>
        /// <param name="mapID">Map ID to initialize</param>
        public static void InitializeAOI(int mapID)
        {
            try
            {
                AOIManager.Instance.InitializeMapGrids(mapID);
                LogHelper.WriteLine(LogLevel.Info, $"AOI system initialized for map {mapID}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error initializing AOI for map {mapID}: {ex.Message}");
            }
        }
        
   
        #endregion
        
        #region Utility Methods
        
        /// <summary>
        /// Check if AOI system is enabled
        /// </summary>
        /// <returns>True if AOI system is enabled</returns>
        public static bool IsAOIEnabled()
        {
            // This can be controlled by a configuration setting
            return true; // For now, always enabled
        }
        
        /// <summary>
        /// Get grid coordinates for a position
        /// </summary>
        /// <param name="posX">X coordinate</param>
        /// <param name="posY">Y coordinate</param>
        /// <param name="mapID">Map ID</param>
        /// <returns>Grid coordinates</returns>
        public static (int gridX, int gridY) GetGridCoordinates(float posX, float posY, int mapID)
        {
            return AOIManager.Instance.GetGridCoordinates(posX, posY, mapID);
        }
        
        #endregion
    }
    
    /// <summary>
    /// AOI statistics for monitoring and debugging
    /// </summary>
    public class AOIStatistics
    {
        public int MapID { get; set; }
        public int TotalGrids { get; set; }
        public int ActiveGrids { get; set; }
        public int TotalPlayers { get; set; }
        public int TotalNPCs { get; set; }
        public int TotalGroundItems { get; set; }
        public int DirtyGrids { get; set; }
        
        public override string ToString()
        {
            return $"AOI Stats - Map:{MapID} Grids:{ActiveGrids}/{TotalGrids} " +
                   $"Players:{TotalPlayers} NPCs:{TotalNPCs} Items:{TotalGroundItems} Dirty:{DirtyGrids}";
        }

        #region AOI Query Helper Methods

        /// <summary>
        /// Get all players within range using AOI system
        /// Replaces the need for PlayerList
        /// </summary>
        /// <param name="centerX">Center X coordinate</param>
        /// <param name="centerY">Center Y coordinate</param>
        /// <param name="mapID">Map ID</param>
        /// <param name="range">Search range</param>
        /// <returns>List of players within range</returns>
        public static List<Players> GetPlayersInRange(float centerX, float centerY, int mapID, int range = 400)
        {
            var result = new List<Players>();
            try
            {
                if (!AOIConfiguration.Instance.ShouldUseAOI(mapID))
                {
                    // Fallback to World.allConnectedChars for non-AOI maps
                    foreach (var player in World.allConnectedChars.Values)
                    {
                        if (player.MapID == mapID &&
                            DistanceHelper.IsWithinRangeSquared(centerX, centerY, player.PosX, player.PosY, range))
                        {
                            result.Add(player);
                        }
                    }
                    return result;
                }

                var aoiGrids = AOIManager.Instance.GetAOIGrids(centerX, centerY, mapID);
                foreach (var grid in aoiGrids)
                {
                    foreach (var player in grid.Players)
                    {
                        if (player != null &&
                            player.Client != null &&
                            player.Client.Running &&
                            DistanceHelper.IsWithinRangeSquared(centerX, centerY, player.PosX, player.PosY, range))
                        {
                            result.Add(player);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error getting players in range: {ex.Message}");
            }
            return result;
        }

        /// <summary>
        /// Get all NPCs within range using AOI system
        /// Replaces the need for NpcList
        /// </summary>
        /// <param name="centerX">Center X coordinate</param>
        /// <param name="centerY">Center Y coordinate</param>
        /// <param name="mapID">Map ID</param>
        /// <param name="range">Search range</param>
        /// <returns>List of NPCs within range</returns>
        public static List<NpcClass> GetNpcsInRange(float centerX, float centerY, int mapID, int range = 400)
        {
            var result = new List<NpcClass>();
            try
            {
                if (!AOIConfiguration.Instance.ShouldUseAOI(mapID))
                {
                    // Fallback to MapClass for non-AOI maps
                    var npcsInMap = MapClass.GetnpcTemplate(mapID);
                    if (npcsInMap != null)
                    {
                        foreach (var npc in npcsInMap.Values)
                        {
                            if (DistanceHelper.IsWithinRangeSquared(centerX, centerY, npc.Rxjh_X, npc.Rxjh_Y, range))
                            {
                                result.Add(npc);
                            }
                        }
                    }
                    return result;
                }

                var aoiGrids = AOIManager.Instance.GetAOIGrids(centerX, centerY, mapID);
                foreach (var grid in aoiGrids)
                {
                    foreach (var npc in grid.NPCs)
                    {
                        if (npc != null &&
                            !npc.NPCDeath &&
                            DistanceHelper.IsWithinRangeSquared(centerX, centerY, npc.Rxjh_X, npc.Rxjh_Y, range))
                        {
                            result.Add(npc);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error getting NPCs in range: {ex.Message}");
            }
            return result;
        }

        /// <summary>
        /// Get all ground items within range using AOI system
        /// Replaces the need for ListOfGroundItems
        /// </summary>
        /// <param name="centerX">Center X coordinate</param>
        /// <param name="centerY">Center Y coordinate</param>
        /// <param name="mapID">Map ID</param>
        /// <param name="range">Search range</param>
        /// <returns>List of ground items within range</returns>
        public static List<GroundItem> GetGroundItemsInRange(float centerX, float centerY, int mapID, int range = 400)
        {
            var result = new List<GroundItem>();
            try
            {
                if (!AOIConfiguration.Instance.ShouldUseAOI(mapID))
                {
                    // Fallback to World.ItmeTeM for non-AOI maps
                    foreach (var item in World.ItmeTeM.Values)
                    {
                        if (item.MapID == mapID &&
                            DistanceHelper.IsWithinRangeSquared(centerX, centerY, item.PosX, item.PosY, range))
                        {
                            result.Add(item);
                        }
                    }
                    return result;
                }

                var aoiGrids = AOIManager.Instance.GetAOIGrids(centerX, centerY, mapID);
                foreach (var grid in aoiGrids)
                {
                    foreach (var item in grid.GroundItems)
                    {
                        if (item != null &&
                            DistanceHelper.IsWithinRangeSquared(centerX, centerY, item.PosX, item.PosY, range))
                        {
                            result.Add(item);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error getting ground items in range: {ex.Message}");
            }
            return result;
        }

        /// <summary>
        /// Get monsters (NPCs with IsNpc == 0) within range for attack targeting
        /// Replaces the need for Monster_AttackList
        /// </summary>
        /// <param name="centerX">Center X coordinate</param>
        /// <param name="centerY">Center Y coordinate</param>
        /// <param name="mapID">Map ID</param>
        /// <param name="range">Search range</param>
        /// <returns>List of monsters within range</returns>
        public static List<NpcClass> GetMonstersInRange(float centerX, float centerY, int mapID, int range = 400)
        {
            var result = new List<NpcClass>();
            try
            {
                var npcs = GetNpcsInRange(centerX, centerY, mapID, range);
                foreach (var npc in npcs)
                {
                    if (npc.IsNpc == 0) // IsNpc == 0 means it's a monster
                    {
                        result.Add(npc);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error getting monsters in range: {ex.Message}");
            }
            return result;
        }

        /// <summary>
        /// Check if a specific player is within range using AOI system
        /// </summary>
        /// <param name="centerX">Center X coordinate</param>
        /// <param name="centerY">Center Y coordinate</param>
        /// <param name="mapID">Map ID</param>
        /// <param name="targetPlayer">Target player to check</param>
        /// <param name="range">Search range</param>
        /// <returns>True if player is within range</returns>
        public static bool IsPlayerInRange(float centerX, float centerY, int mapID, Players targetPlayer, int range = 400)
        {
            try
            {
                if (targetPlayer == null || targetPlayer.MapID != mapID)
                    return false;

                return DistanceHelper.IsWithinRangeSquared(centerX, centerY, targetPlayer.PosX, targetPlayer.PosY, range);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error checking if player is in range: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Check if a specific NPC is within range using AOI system
        /// </summary>
        /// <param name="centerX">Center X coordinate</param>
        /// <param name="centerY">Center Y coordinate</param>
        /// <param name="mapID">Map ID</param>
        /// <param name="targetNpc">Target NPC to check</param>
        /// <param name="range">Search range</param>
        /// <returns>True if NPC is within range</returns>
        public static bool IsNpcInRange(float centerX, float centerY, int mapID, NpcClass targetNpc, int range = 400)
        {
            try
            {
                if (targetNpc == null || targetNpc.Rxjh_Map != mapID)
                    return false;

                return DistanceHelper.IsWithinRangeSquared(centerX, centerY, targetNpc.Rxjh_X, targetNpc.Rxjh_Y, range);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error checking if NPC is in range: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get players within range for a specific player (excludes self)
        /// Replaces PlayerList usage in Players class
        /// </summary>
        /// <param name="player">The player to search around</param>
        /// <param name="range">Search range</param>
        /// <returns>List of other players within range</returns>
        public static List<Players> GetOtherPlayersInRange(this Players player, int range = 400)
        {
            var result = new List<Players>();
            try
            {
                if (player == null) return result;

                var allPlayers = GetPlayersInRange(player.PosX, player.PosY, player.MapID, range);
                foreach (var otherPlayer in allPlayers)
                {
                    if (otherPlayer.SessionID != player.SessionID) // Exclude self
                    {
                        result.Add(otherPlayer);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error getting other players in range for {player.CharacterName}: {ex.Message}");
            }
            return result;
        }

        /// <summary>
        /// Get NPCs within range for a specific player
        /// Replaces NpcList usage in Players class
        /// </summary>
        /// <param name="player">The player to search around</param>
        /// <param name="range">Search range</param>
        /// <returns>List of NPCs within range</returns>
        public static List<NpcClass> GetNpcsInRange(this Players player, int range = 400)
        {
            try
            {
                if (player == null) return new List<NpcClass>();
                return GetNpcsInRange(player.PosX, player.PosY, player.MapID, range);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error getting NPCs in range for {player.CharacterName}: {ex.Message}");
                return new List<NpcClass>();
            }
        }

        /// <summary>
        /// Get ground items within range for a specific player
        /// Replaces ListOfGroundItems usage in Players class
        /// </summary>
        /// <param name="player">The player to search around</param>
        /// <param name="range">Search range</param>
        /// <returns>List of ground items within range</returns>
        public static List<GroundItem> GetGroundItemsInRange(this Players player, int range = 400)
        {
            try
            {
                if (player == null) return new List<GroundItem>();
                return GetGroundItemsInRange(player.PosX, player.PosY, player.MapID, range);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error getting ground items in range for {player.CharacterName}: {ex.Message}");
                return new List<GroundItem>();
            }
        }

        /// <summary>
        /// Get monsters within range for a specific player
        /// Replaces Monster_AttackList usage in Players class
        /// </summary>
        /// <param name="player">The player to search around</param>
        /// <param name="range">Search range</param>
        /// <returns>List of monsters within range</returns>
        public static List<NpcClass> GetMonstersInRange(this Players player, int range = 400)
        {
            try
            {
                if (player == null) return new List<NpcClass>();
                return GetMonstersInRange(player.PosX, player.PosY, player.MapID, range);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error getting monsters in range for {player.CharacterName}: {ex.Message}");
                return new List<NpcClass>();
            }
        }

        /// <summary>
        /// Get players within range for a specific NPC
        /// Replaces PlayList usage in NpcClass
        /// </summary>
        /// <param name="npc">The NPC to search around</param>
        /// <param name="range">Search range</param>
        /// <returns>List of players within range</returns>
        public static List<Players> GetPlayersInRange(this NpcClass npc, int range = 400)
        {
            try
            {
                if (npc == null) return new List<Players>();
                return GetPlayersInRange(npc.Rxjh_X, npc.Rxjh_Y, npc.Rxjh_Map, range);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error getting players in range for NPC {npc.NPC_SessionID}: {ex.Message}");
                return new List<Players>();
            }
        }

        /// <summary>
        /// Get players within range for a specific ground item
        /// Replaces PlayerList usage in GroundItem
        /// </summary>
        /// <param name="item">The ground item to search around</param>
        /// <param name="range">Search range</param>
        /// <returns>List of players within range</returns>
        public static List<Players> GetPlayersInRange(this GroundItem item, int range = 400)
        {
            try
            {
                if (item == null) return new List<Players>();
                return GetPlayersInRange(item.PosX, item.PosY, item.MapID, range);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error getting players in range for ground item {item.id}: {ex.Message}");
                return new List<Players>();
            }
        }

        /// <summary>
        /// Execute an action for each player within range (optimized iteration)
        /// </summary>
        /// <param name="centerX">Center X coordinate</param>
        /// <param name="centerY">Center Y coordinate</param>
        /// <param name="mapID">Map ID</param>
        /// <param name="range">Search range</param>
        /// <param name="action">Action to execute for each player</param>
        public static void ForEachPlayerInRange(float centerX, float centerY, int mapID, int range, Action<Players> action)
        {
            try
            {
                if (action == null) return;

                if (!AOIConfiguration.Instance.ShouldUseAOI(mapID))
                {
                    // Fallback to World.allConnectedChars for non-AOI maps
                    foreach (var player in World.allConnectedChars.Values)
                    {
                        if (player.MapID == mapID &&
                            DistanceHelper.IsWithinRangeSquared(centerX, centerY, player.PosX, player.PosY, range))
                        {
                            action(player);
                        }
                    }
                    return;
                }

                var aoiGrids = AOIManager.Instance.GetAOIGrids(centerX, centerY, mapID);
                foreach (var grid in aoiGrids)
                {
                    grid.ForEachPlayer(player =>
                    {
                        if (player != null &&
                            player.Client != null &&
                            player.Client.Running &&
                            DistanceHelper.IsWithinRangeSquared(centerX, centerY, player.PosX, player.PosY, range))
                        {
                            action(player);
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error executing action for each player in range: {ex.Message}");
            }
        }

        #endregion
    }
}
